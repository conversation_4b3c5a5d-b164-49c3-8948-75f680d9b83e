# SQQ API - Sistema de Autenticación

API completa de autenticación y gestión de usuarios desarrollada con NestJS, PostgreSQL y JWT.

## 🚀 Características

- ✅ **Autenticación JWT** completa con access y refresh tokens
- ✅ **Registro y login** de usuarios
- ✅ **Gestión de usuarios** con roles (USER/ADMIN)
- ✅ **Base de datos PostgreSQL** con TypeORM
- ✅ **Validación robusta** con class-validator
- ✅ **Documentación Swagger** interactiva
- ✅ **Tests unitarios** completos
- ✅ **Arquitectura escalable** y bien estructurada
- ✅ **Manejo de errores** centralizado
- ✅ **Seguridad** con bcrypt y guards

## 📋 Requisitos Previos

- Node.js 20+ (usar `nvm use 20`)
- PostgreSQL 12+
- npm o yarn

## 🛠️ Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd sqq-api
```

2. **Usar Node.js 20**
```bash
nvm use 20
```

3. **Instalar dependencias**
```bash
npm install
```

4. **Configurar variables de entorno**
```bash
cp .env.example .env
```

Editar `.env` con tus configuraciones:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=tu_password
DB_DATABASE=sqq_db

# JWT Configuration
JWT_SECRET=tu-super-secreto-jwt-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=tu-super-secreto-refresh-key
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
PORT=3000
NODE_ENV=development

# Security
BCRYPT_ROUNDS=12
```

5. **Crear la base de datos**
```sql
CREATE DATABASE sqq_db;
```

## 🚀 Ejecución

```bash
# Desarrollo con hot reload
npm run start:dev

# Producción
npm run start:prod

# Modo debug
npm run start:debug
```

La aplicación estará disponible en:
- **API**: http://localhost:3000
- **Swagger**: http://localhost:3000/api

## 📚 Documentación API

### Endpoints de Autenticación

#### POST /auth/register
Registrar nuevo usuario
```json
{
  "email": "<EMAIL>",
  "firstName": "Juan",
  "lastName": "Pérez",
  "password": "MiPassword123!"
}
```

#### POST /auth/login
Iniciar sesión
```json
{
  "email": "<EMAIL>",
  "password": "MiPassword123!"
}
```

#### POST /auth/refresh
Renovar tokens
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### POST /auth/logout
Cerrar sesión (requiere autenticación)

#### GET /auth/profile
Obtener perfil del usuario (requiere autenticación)

### Endpoints de Usuarios

#### GET /users
Listar todos los usuarios (solo ADMIN)

#### GET /users/:id
Obtener usuario por ID

#### POST /users
Crear usuario (solo ADMIN)

#### PATCH /users/:id
Actualizar usuario

#### DELETE /users/:id
Eliminar usuario (solo ADMIN)

## 🧪 Testing

```bash
# Tests unitarios
npm run test

# Tests con coverage
npm run test:cov

# Tests en modo watch
npm run test:watch

# Tests e2e
npm run test:e2e
```

## 🏗️ Arquitectura

```
src/
├── auth/                 # Módulo de autenticación
│   ├── decorators/      # Decoradores personalizados
│   ├── dto/             # DTOs de autenticación
│   ├── guards/          # Guards de seguridad
│   ├── interfaces/      # Interfaces TypeScript
│   └── strategies/      # Estrategias Passport
├── common/              # Código compartido
│   ├── filters/         # Filtros de excepción
│   └── pipes/           # Pipes de validación
├── config/              # Configuración de la app
├── database/            # Configuración de base de datos
└── users/               # Módulo de usuarios
    ├── dto/             # DTOs de usuarios
    └── entities/        # Entidades TypeORM
```

## 🔐 Seguridad

- **Passwords**: Hasheados con bcrypt (12 rounds)
- **JWT**: Tokens seguros con expiración
- **Refresh Tokens**: Almacenados hasheados en BD
- **Validación**: Validación estricta de entrada
- **Guards**: Protección de rutas por roles
- **CORS**: Configurado para desarrollo/producción

## 🚀 Despliegue

### Variables de Entorno de Producción
```env
NODE_ENV=production
JWT_SECRET=clave-super-secreta-produccion
JWT_REFRESH_SECRET=clave-refresh-super-secreta-produccion
DB_HOST=tu-host-produccion
# ... otras variables
```

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT.